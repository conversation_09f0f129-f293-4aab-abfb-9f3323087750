# Laravel Fractal Implementation Guide

## Table of Contents

- [Overview](#overview)
- [Installation & Setup](#installation--setup)
  - [1.1. Package Installation](#11-package-installation)
  - [1.2. Configuration Publishing](#12-configuration-publishing)
  - [1.3. Basic Setup](#13-basic-setup)
- [Transformer Creation](#transformer-creation)
  - [2.1. Basic Transformers](#21-basic-transformers)
  - [2.2. Advanced Transformers](#22-advanced-transformers)
  - [2.3. Nested Transformers](#23-nested-transformers)
- [Resource Management](#resource-management)
  - [3.1. Item Resources](#31-item-resources)
  - [3.2. Collection Resources](#32-collection-resources)
  - [3.3. Relationship Handling](#33-relationship-handling)
- [Pagination & Filtering](#pagination--filtering)
  - [4.1. Pagination Setup](#41-pagination-setup)
  - [4.2. Advanced Filtering](#42-advanced-filtering)
  - [4.3. Sorting & Ordering](#43-sorting--ordering)
- [API Response Formatting](#api-response-formatting)
- [Caching Integration](#caching-integration)
- [Performance Optimization](#performance-optimization)
- [Testing Strategies](#testing-strategies)
- [Best Practices](#best-practices)
- [Navigation](#navigation)

## Overview

Laravel Fractal provides advanced API transformation layers with flexible resource management, relationship handling, and comprehensive pagination support. This guide covers enterprise-level implementation with caching integration, performance optimization, and modern API patterns.

**🚀 Key Features:**
- **Flexible Transformations**: Customizable data transformation with includes/excludes
- **Relationship Handling**: Efficient nested resource loading and transformation
- **Pagination Support**: Built-in pagination with metadata and navigation links
- **Caching Integration**: Response caching for improved performance
- **API Versioning**: Support for multiple API versions and backward compatibility
- **Performance Optimization**: Lazy loading and efficient data processing

## Installation & Setup

### 1.1. Package Installation

Install Laravel Fractal using Composer:

```bash
# Install Laravel Fractal
composer require spatie/laravel-fractal

# Publish configuration (optional)
php artisan vendor:publish --provider="Spatie\Fractal\FractalServiceProvider"
```

### 1.2. Configuration Publishing

Publish and configure the Fractal configuration file:

```bash
# Publish configuration
php artisan vendor:publish --tag=fractal-config
```

Configuration file (`config/fractal.php`):

```php
<?php

return [
    /*
     * The default serializer to be used when performing a transformation.
     */
    'default_serializer' => \League\Fractal\Serializer\DataArraySerializer::class,

    /*
     * The default paginator to be used when transforming paginated results.
     */
    'default_paginator' => \League\Fractal\Paginator\IlluminatePaginatorAdapter::class,

    /*
     * League\Fractal\Manager settings
     */
    'auto_includes' => [
        'request_key' => 'include',
    ],
];
```

### 1.3. Basic Setup

Create a base transformer class for consistent API responses:

```php
<?php

namespace App\Transformers;

use League\Fractal\TransformerAbstract;

abstract class BaseTransformer extends TransformerAbstract
{
    /**
     * Common fields for all models
     */
    protected function getBaseFields($model): array
    {
        return [
            'id' => $model->public_id ?? $model->id,
            'created_at' => $model->created_at?->toISOString(),
            'updated_at' => $model->updated_at?->toISOString(),
        ];
    }

    /**
     * Include user stamps if available
     */
    protected function getUserStamps($model): array
    {
        $stamps = [];

        if ($model->created_by) {
            $stamps['created_by'] = [
                'id' => $model->createdBy->public_id,
                'name' => $model->createdBy->name,
            ];
        }

        if ($model->updated_by) {
            $stamps['updated_by'] = [
                'id' => $model->updatedBy->public_id,
                'name' => $model->updatedBy->name,
            ];
        }

        return $stamps;
    }
}
```

## Transformer Creation

### 2.1. Basic Transformers

Create transformers for core Chinook models:

```php
<?php

namespace App\Transformers;

use App\Models\Artist;

class ArtistTransformer extends BaseTransformer
{
    /**
     * Available includes
     */
    protected array $availableIncludes = [
        'albums',
        'categories',
        'media',
    ];

    /**
     * Default includes
     */
    protected array $defaultIncludes = [
        'categories',
    ];

    /**
     * Transform artist data
     */
    public function transform(Artist $artist): array
    {
        return array_merge($this->getBaseFields($artist), [
            'name' => $artist->name,
            'slug' => $artist->slug,
            'bio' => $artist->bio,
            'website' => $artist->website,
            'formed_year' => $artist->formed_year,
            'albums_count' => $artist->albums_count ?? $artist->albums()->count(),
        ], $this->getUserStamps($artist));
    }

    /**
     * Include albums
     */
    public function includeAlbums(Artist $artist)
    {
        return $this->collection($artist->albums, new AlbumTransformer);
    }

    /**
     * Include categories
     */
    public function includeCategories(Artist $artist)
    {
        return $this->collection($artist->categories, new CategoryTransformer);
    }

    /**
     * Include media
     */
    public function includeMedia(Artist $artist)
    {
        return $this->collection($artist->getMedia(), new MediaTransformer);
    }
}
```

### 2.2. Advanced Transformers

Album transformer with complex relationships:

```php
<?php

namespace App\Transformers;

use App\Models\Album;

class AlbumTransformer extends BaseTransformer
{
    protected array $availableIncludes = [
        'artist',
        'tracks',
        'categories',
        'media',
        'sales_stats',
    ];

    protected array $defaultIncludes = [
        'artist',
    ];

    public function transform(Album $album): array
    {
        return array_merge($this->getBaseFields($album), [
            'title' => $album->title,
            'slug' => $album->slug,
            'release_date' => $album->release_date?->toDateString(),
            'unit_price' => $album->unit_price,
            'tracks_count' => $album->tracks_count ?? $album->tracks()->count(),
            'total_duration' => $album->total_duration,
            'cover_art_url' => $album->getFirstMediaUrl('covers'),
        ], $this->getUserStamps($album));
    }

    public function includeArtist(Album $album)
    {
        return $this->item($album->artist, new ArtistTransformer);
    }

    public function includeTracks(Album $album)
    {
        return $this->collection($album->tracks, new TrackTransformer);
    }

    public function includeSalesStats(Album $album)
    {
        return $this->item($album, function (Album $album) {
            return [
                'total_sales' => $album->invoiceLines()->sum('quantity'),
                'revenue' => $album->invoiceLines()->sum('total'),
                'last_sale' => $album->invoiceLines()
                    ->latest('created_at')
                    ->first()?->created_at?->toISOString(),
            ];
        });
    }
}
```

### 2.3. Nested Transformers

Track transformer with deep nesting:

```php
<?php

namespace App\Transformers;

use App\Models\Track;

class TrackTransformer extends BaseTransformer
{
    protected array $availableIncludes = [
        'album',
        'album.artist',
        'media_type',
        'categories',
        'playlists',
        'invoice_lines',
    ];

    public function transform(Track $track): array
    {
        return array_merge($this->getBaseFields($track), [
            'name' => $track->name,
            'slug' => $track->slug,
            'composer' => $track->composer,
            'duration' => [
                'milliseconds' => $track->milliseconds,
                'formatted' => $track->formatted_duration,
            ],
            'file_size' => [
                'bytes' => $track->bytes,
                'formatted' => $track->formatted_file_size,
            ],
            'unit_price' => $track->unit_price,
            'track_number' => $track->track_number,
            'audio_url' => $track->getFirstMediaUrl('audio'),
        ], $this->getUserStamps($track));
    }

    public function includeAlbum(Track $track)
    {
        return $this->item($track->album, new AlbumTransformer);
    }

    public function includeMediaType(Track $track)
    {
        return $this->item($track->mediaType, new MediaTypeTransformer);
    }
}
```

## Resource Management

### 3.1. Item Resources

Transform single model instances:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Artist;
use App\Transformers\ArtistTransformer;
use Spatie\Fractal\Fractal;

class ArtistController extends Controller
{
    public function show(Artist $artist)
    {
        return Fractal::create()
            ->item($artist)
            ->transformWith(ArtistTransformer::class)
            ->includeFromRequest()
            ->respond();
    }

    public function update(Request $request, Artist $artist)
    {
        $artist->update($request->validated());

        return Fractal::create()
            ->item($artist->fresh())
            ->transformWith(ArtistTransformer::class)
            ->respond(200);
    }
}
```

### 3.2. Collection Resources

Transform collections with pagination:

```php
public function index(Request $request)
{
    $artists = Artist::query()
        ->withCount(['albums', 'tracks'])
        ->with(['categories'])
        ->when($request->search, function ($query, $search) {
            $query->where('name', 'like', "%{$search}%");
        })
        ->when($request->category, function ($query, $category) {
            $query->whereHas('categories', function ($q) use ($category) {
                $q->where('slug', $category);
            });
        })
        ->paginate($request->per_page ?? 15);

    return Fractal::create()
        ->collection($artists)
        ->transformWith(ArtistTransformer::class)
        ->paginateWith(new IlluminatePaginatorAdapter($artists))
        ->includeFromRequest()
        ->respond();
}
```

### 3.3. Relationship Handling

Handle complex nested relationships:

```php
public function albums(Artist $artist)
{
    $albums = $artist->albums()
        ->with(['tracks', 'categories'])
        ->withCount('tracks')
        ->paginate(10);

    return Fractal::create()
        ->collection($albums)
        ->transformWith(AlbumTransformer::class)
        ->paginateWith(new IlluminatePaginatorAdapter($albums))
        ->respond();
}

public function tracks(Artist $artist)
{
    $tracks = $artist->tracks()
        ->with(['album', 'mediaType', 'categories'])
        ->orderBy('album_id')
        ->orderBy('track_number')
        ->paginate(20);

    return Fractal::create()
        ->collection($tracks)
        ->transformWith(TrackTransformer::class)
        ->paginateWith(new IlluminatePaginatorAdapter($tracks))
        ->respond();
}
```

## Pagination & Filtering

### 4.1. Pagination Setup

Configure pagination with metadata:

```php
<?php

namespace App\Http\Controllers\Api;

use League\Fractal\Pagination\IlluminatePaginatorAdapter;

class BaseApiController extends Controller
{
    protected function paginatedResponse($query, $transformer, $perPage = 15)
    {
        $paginator = $query->paginate($perPage);

        return Fractal::create()
            ->collection($paginator)
            ->transformWith($transformer)
            ->paginateWith(new IlluminatePaginatorAdapter($paginator))
            ->addMeta([
                'pagination' => [
                    'total' => $paginator->total(),
                    'per_page' => $paginator->perPage(),
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'from' => $paginator->firstItem(),
                    'to' => $paginator->lastItem(),
                ],
            ])
            ->respond();
    }
}
```

### 4.2. Advanced Filtering

Implement complex filtering logic:

```php
public function index(Request $request)
{
    $query = Track::query()
        ->with(['album.artist', 'mediaType', 'categories'])
        ->withCount('invoiceLines');

    // Genre filtering
    if ($request->genre) {
        $query->whereHas('categories', function ($q) use ($request) {
            $q->where('type', CategoryType::GENRE)
              ->where('slug', $request->genre);
        });
    }

    // Price range filtering
    if ($request->min_price || $request->max_price) {
        $query->whereBetween('unit_price', [
            $request->min_price ?? 0,
            $request->max_price ?? 999.99
        ]);
    }

    // Duration filtering
    if ($request->min_duration || $request->max_duration) {
        $query->whereBetween('milliseconds', [
            ($request->min_duration ?? 0) * 1000,
            ($request->max_duration ?? 1800) * 1000
        ]);
    }

    // Artist filtering
    if ($request->artist) {
        $query->whereHas('album.artist', function ($q) use ($request) {
            $q->where('slug', $request->artist);
        });
    }

    return $this->paginatedResponse(
        $query,
        TrackTransformer::class,
        $request->per_page ?? 20
    );
}
```

### 4.3. Sorting & Ordering

Implement flexible sorting:

```php
public function index(Request $request)
{
    $query = Album::query()
        ->with(['artist', 'categories'])
        ->withCount(['tracks', 'invoiceLines']);

    // Dynamic sorting
    $sortField = $request->sort ?? 'title';
    $sortDirection = $request->direction ?? 'asc';

    switch ($sortField) {
        case 'artist':
            $query->join('artists', 'albums.artist_id', '=', 'artists.id')
                  ->orderBy('artists.name', $sortDirection)
                  ->select('albums.*');
            break;

        case 'release_date':
            $query->orderBy('release_date', $sortDirection);
            break;

        case 'tracks_count':
            $query->orderBy('tracks_count', $sortDirection);
            break;

        case 'popularity':
            $query->orderBy('invoice_lines_count', $sortDirection);
            break;

        default:
            $query->orderBy($sortField, $sortDirection);
    }

    return $this->paginatedResponse($query, AlbumTransformer::class);
}

# Install additional dependencies for enhanced features
composer require league/fractal

# Verify installation
php artisan fractal:check
```

**System Requirements:**

- PHP 8.1 or higher
- Laravel 9.0 or higher
- League Fractal 0.20 or higher

### 1.2. Configuration Publishing

Configure Laravel Fractal for your application:

```php
// config/fractal.php
return [
    /*
     * The default serializer to be used when performing a transformation. It
     * may be left empty to use Fractal's default one. This can either be a
     * string or a League\Fractal\Serializer\SerializerAbstract subclass.
     */
    'default_serializer' => \Spatie\Fractal\ArraySerializer::class,

    /*
     * The default paginator to be used when performing a transformation. It
     * may be left empty to use Fractal's default one. This can either be a
     * string or a League\Fractal\Paginator\PaginatorInterface subclass.
     */
    'default_paginator' => \Spatie\Fractal\PaginatorAdapter::class,

    /*
     * League\Fractal\Manager settings
     */
    'auto_includes' => [
        'enabled' => true,
        'request_key' => 'include',
    ],

    'auto_excludes' => [
        'enabled' => true,
        'request_key' => 'exclude',
    ],

    /*
     * If you wish to override or extend the default Fractal Manager instance
     * provide the name of the class here.
     */
    'fractal_class' => \Spatie\Fractal\Fractal::class,

    /*
     * The key that should be used to wrap the transformed data for
     * a collection of items.
     */
    'collection_key' => 'data',

    /*
     * The key that should be used to wrap the transformed data for
     * a single item.
     */
    'item_key' => 'data',
];
```

### 1.3. Basic Setup

Create your first transformer:

```php
// app/Transformers/UserTransformer.php
<?php

namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;

class UserTransformer extends TransformerAbstract
{
    protected array $availableIncludes = [
        'profile',
        'orders',
        'preferences',
    ];

    protected array $defaultIncludes = [
        'profile',
    ];

    public function transform(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'created_at' => $user->created_at->toISOString(),
            'updated_at' => $user->updated_at->toISOString(),
        ];
    }

    public function includeProfile(User $user)
    {
        if ($user->profile) {
            return $this->item($user->profile, new ProfileTransformer());
        }

        return $this->null();
    }

    public function includeOrders(User $user)
    {
        return $this->collection($user->orders, new OrderTransformer());
    }

    public function includePreferences(User $user)
    {
        return $this->item($user->preferences ?? [], function ($preferences) {
            return $preferences;
        });
    }
}
```

**Basic Usage:**

```php
// In your controller
use Spatie\Fractal\Fractal;
use App\Transformers\UserTransformer;

public function show(User $user)
{
    return Fractal::create()
        ->item($user)
        ->transformWith(UserTransformer::class)
        ->toArray();
}

public function index()
{
    $users = User::paginate(15);
    
    return Fractal::create()
        ->collection($users)
        ->transformWith(UserTransformer::class)
        ->paginateWith(new IlluminatePaginatorAdapter($users))
        ->toArray();
}
```

## Transformer Creation

### 2.1. Basic Transformers

Create comprehensive transformers for your models:

```php
// app/Transformers/ProductTransformer.php
<?php

namespace App\Transformers;

use App\Models\Product;
use League\Fractal\TransformerAbstract;

class ProductTransformer extends TransformerAbstract
{
    protected array $availableIncludes = [
        'category',
        'images',
        'reviews',
        'variants',
        'related_products',
    ];

    protected array $defaultIncludes = [
        'category',
    ];

    public function transform(Product $product): array
    {
        return [
            'id' => $product->id,
            'name' => $product->name,
            'description' => $product->description,
            'sku' => $product->sku,
            'price' => [
                'amount' => $product->price,
                'formatted' => '$' . number_format($product->price, 2),
                'currency' => 'USD',
            ],
            'stock' => [
                'quantity' => $product->stock_quantity,
                'in_stock' => $product->stock_quantity > 0,
                'low_stock' => $product->stock_quantity <= 10 && $product->stock_quantity > 0,
            ],
            'status' => [
                'is_active' => $product->is_active,
                'is_featured' => $product->is_featured,
                'is_on_sale' => $product->sale_price !== null,
            ],
            'metadata' => [
                'weight' => $product->weight,
                'dimensions' => $product->dimensions,
                'tags' => $product->tags,
            ],
            'timestamps' => [
                'created_at' => $product->created_at->toISOString(),
                'updated_at' => $product->updated_at->toISOString(),
            ],
        ];
    }

    public function includeCategory(Product $product)
    {
        if ($product->category) {
            return $this->item($product->category, new CategoryTransformer());
        }

        return $this->null();
    }

    public function includeImages(Product $product)
    {
        return $this->collection($product->images, new ProductImageTransformer());
    }

    public function includeReviews(Product $product)
    {
        return $this->collection($product->reviews, new ReviewTransformer());
    }

    public function includeVariants(Product $product)
    {
        return $this->collection($product->variants, new ProductVariantTransformer());
    }

    public function includeRelatedProducts(Product $product)
    {
        $related = $product->getRelatedProducts(5);
        return $this->collection($related, new self());
    }
}
```

### 2.2. Advanced Transformers

Implement advanced transformation logic:

```php
// app/Transformers/OrderTransformer.php
<?php

namespace App\Transformers;

use App\Models\Order;
use League\Fractal\TransformerAbstract;

class OrderTransformer extends TransformerAbstract
{
    protected array $availableIncludes = [
        'user',
        'items',
        'shipping_address',
        'billing_address',
        'payments',
        'shipments',
    ];

    protected array $defaultIncludes = [
        'items',
    ];

    public function transform(Order $order): array
    {
        return [
            'id' => $order->id,
            'order_number' => $order->order_number,
            'status' => [
                'current' => $order->status,
                'label' => $order->getStatusLabel(),
                'can_cancel' => $order->canBeCancelled(),
                'can_return' => $order->canBeReturned(),
            ],
            'totals' => [
                'subtotal' => $this->formatMoney($order->subtotal),
                'tax' => $this->formatMoney($order->tax_amount),
                'shipping' => $this->formatMoney($order->shipping_amount),
                'discount' => $this->formatMoney($order->discount_amount),
                'total' => $this->formatMoney($order->total),
            ],
            'dates' => [
                'ordered_at' => $order->created_at->toISOString(),
                'shipped_at' => $order->shipped_at?->toISOString(),
                'delivered_at' => $order->delivered_at?->toISOString(),
                'estimated_delivery' => $order->getEstimatedDeliveryDate()?->toISOString(),
            ],
            'tracking' => [
                'number' => $order->tracking_number,
                'url' => $order->getTrackingUrl(),
                'carrier' => $order->shipping_carrier,
            ],
            'metadata' => [
                'notes' => $order->notes,
                'gift_message' => $order->gift_message,
                'source' => $order->source,
            ],
        ];
    }

    public function includeUser(Order $order)
    {
        return $this->item($order->user, new UserTransformer());
    }

    public function includeItems(Order $order)
    {
        return $this->collection($order->items, new OrderItemTransformer());
    }

    public function includeShippingAddress(Order $order)
    {
        if ($order->shipping_address) {
            return $this->item($order->shipping_address, new AddressTransformer());
        }

        return $this->null();
    }

    public function includeBillingAddress(Order $order)
    {
        if ($order->billing_address) {
            return $this->item($order->billing_address, new AddressTransformer());
        }

        return $this->null();
    }

    public function includePayments(Order $order)
    {
        return $this->collection($order->payments, new PaymentTransformer());
    }

    public function includeShipments(Order $order)
    {
        return $this->collection($order->shipments, new ShipmentTransformer());
    }

    private function formatMoney(float $amount): array
    {
        return [
            'amount' => $amount,
            'formatted' => '$' . number_format($amount, 2),
            'currency' => 'USD',
        ];
    }
}
```

### 2.3. Nested Transformers

Handle complex nested relationships:

```php
// app/Transformers/OrderItemTransformer.php
<?php

namespace App\Transformers;

use App\Models\OrderItem;
use League\Fractal\TransformerAbstract;

class OrderItemTransformer extends TransformerAbstract
{
    protected array $availableIncludes = [
        'product',
        'variant',
        'customizations',
    ];

    protected array $defaultIncludes = [
        'product',
    ];

    public function transform(OrderItem $item): array
    {
        return [
            'id' => $item->id,
            'quantity' => $item->quantity,
            'pricing' => [
                'unit_price' => $this->formatMoney($item->unit_price),
                'total_price' => $this->formatMoney($item->total_price),
                'discount_amount' => $this->formatMoney($item->discount_amount),
                'tax_amount' => $this->formatMoney($item->tax_amount),
            ],
            'product_snapshot' => [
                'name' => $item->product_name,
                'sku' => $item->product_sku,
                'description' => $item->product_description,
            ],
            'fulfillment' => [
                'status' => $item->fulfillment_status,
                'shipped_quantity' => $item->shipped_quantity,
                'remaining_quantity' => $item->quantity - $item->shipped_quantity,
            ],
            'metadata' => [
                'notes' => $item->notes,
                'gift_wrap' => $item->gift_wrap,
                'personalization' => $item->personalization,
            ],
        ];
    }

    public function includeProduct(OrderItem $item)
    {
        if ($item->product) {
            return $this->item($item->product, new ProductTransformer());
        }

        return $this->null();
    }

    public function includeVariant(OrderItem $item)
    {
        if ($item->variant) {
            return $this->item($item->variant, new ProductVariantTransformer());
        }

        return $this->null();
    }

    public function includeCustomizations(OrderItem $item)
    {
        return $this->collection($item->customizations, function ($customization) {
            return [
                'type' => $customization['type'],
                'value' => $customization['value'],
                'price' => $this->formatMoney($customization['price'] ?? 0),
            ];
        });
    }

    private function formatMoney(float $amount): array
    {
        return [
            'amount' => $amount,
            'formatted' => '$' . number_format($amount, 2),
            'currency' => 'USD',
        ];
    }
}
```

## Resource Management

### 3.1. Item Resources

Create efficient item resource handling:

```php
// app/Http/Controllers/Api/ProductController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Transformers\ProductTransformer;
use Spatie\Fractal\Fractal;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    public function show(Request $request, Product $product): JsonResponse
    {
        $fractal = Fractal::create()
            ->item($product)
            ->transformWith(ProductTransformer::class);

        // Handle includes from request
        if ($request->has('include')) {
            $fractal->parseIncludes($request->get('include'));
        }

        // Handle excludes from request
        if ($request->has('exclude')) {
            $fractal->parseExcludes($request->get('exclude'));
        }

        return response()->json($fractal->toArray());
    }

    public function showWithCustomIncludes(Product $product): JsonResponse
    {
        return response()->json(
            Fractal::create()
                ->item($product)
                ->transformWith(ProductTransformer::class)
                ->parseIncludes(['category', 'images', 'reviews.user'])
                ->toArray()
        );
    }

    public function showMinimal(Product $product): JsonResponse
    {
        return response()->json(
            Fractal::create()
                ->item($product)
                ->transformWith(ProductTransformer::class)
                ->parseExcludes(['metadata', 'timestamps'])
                ->toArray()
        );
    }
}
```

### 3.2. Collection Resources

Handle collections with advanced features:

```php
// app/Http/Controllers/Api/ProductCollectionController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Transformers\ProductTransformer;
use Spatie\Fractal\Fractal;
use League\Fractal\Pagination\IlluminatePaginatorAdapter;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductCollectionController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Product::query()
            ->with(['category', 'images'])
            ->where('is_active', true);

        // Apply filters
        if ($request->has('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->get('category'));
            });
        }

        if ($request->has('price_min')) {
            $query->where('price', '>=', $request->get('price_min'));
        }

        if ($request->has('price_max')) {
            $query->where('price', '<=', $request->get('price_max'));
        }

        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $search = $request->get('search');
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $products = $query->paginate($request->get('per_page', 15));

        $fractal = Fractal::create()
            ->collection($products->getCollection())
            ->transformWith(ProductTransformer::class)
            ->paginateWith(new IlluminatePaginatorAdapter($products));

        // Handle includes
        if ($request->has('include')) {
            $fractal->parseIncludes($request->get('include'));
        }

        return response()->json($fractal->toArray());
    }

    public function featured(): JsonResponse
    {
        $products = Product::where('is_featured', true)
            ->where('is_active', true)
            ->with(['category', 'images'])
            ->limit(10)
            ->get();

        return response()->json(
            Fractal::create()
                ->collection($products)
                ->transformWith(ProductTransformer::class)
                ->parseIncludes(['category', 'images'])
                ->toArray()
        );
    }

    public function related(Product $product): JsonResponse
    {
        $related = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('is_active', true)
            ->inRandomOrder()
            ->limit(6)
            ->get();

        return response()->json(
            Fractal::create()
                ->collection($related)
                ->transformWith(ProductTransformer::class)
                ->parseIncludes(['category'])
                ->toArray()
        );
    }
}
```

### 3.3. Relationship Handling

Efficiently handle complex relationships:

```php
// app/Transformers/UserTransformer.php (Enhanced)
<?php

namespace App\Transformers;

use App\Models\User;
use League\Fractal\TransformerAbstract;
use League\Fractal\Resource\Collection;
use League\Fractal\Resource\Item;

class UserTransformer extends TransformerAbstract
{
    protected array $availableIncludes = [
        'profile',
        'orders',
        'orders.items',
        'orders.items.product',
        'wishlist',
        'addresses',
        'payment_methods',
        'reviews',
        'notifications',
    ];

    protected array $defaultIncludes = [
        'profile',
    ];

    public function transform(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'status' => [
                'is_active' => $user->is_active,
                'is_verified' => $user->email_verified_at !== null,
                'last_login' => $user->last_login_at?->toISOString(),
            ],
            'statistics' => [
                'orders_count' => $user->orders_count ?? $user->orders()->count(),
                'total_spent' => $user->orders()->sum('total'),
                'average_order_value' => $user->getAverageOrderValue(),
                'lifetime_value' => $user->getLifetimeValue(),
            ],
            'timestamps' => [
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString(),
            ],
        ];
    }

    public function includeProfile(User $user): ?Item
    {
        if ($user->profile) {
            return $this->item($user->profile, new UserProfileTransformer());
        }

        return $this->null();
    }

    public function includeOrders(User $user): Collection
    {
        return $this->collection($user->orders, new OrderTransformer());
    }

    public function includeWishlist(User $user): Collection
    {
        return $this->collection($user->wishlistItems, new WishlistItemTransformer());
    }

    public function includeAddresses(User $user): Collection
    {
        return $this->collection($user->addresses, new AddressTransformer());
    }

    public function includePaymentMethods(User $user): Collection
    {
        return $this->collection($user->paymentMethods, new PaymentMethodTransformer());
    }

    public function includeReviews(User $user): Collection
    {
        return $this->collection($user->reviews, new ReviewTransformer());
    }

    public function includeNotifications(User $user): Collection
    {
        return $this->collection($user->notifications, new NotificationTransformer());
    }
}
```

## Pagination & Filtering

### 4.1. Pagination Setup

Implement comprehensive pagination:

```php
// app/Http/Controllers/Api/PaginatedController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Spatie\Fractal\Fractal;
use League\Fractal\Pagination\IlluminatePaginatorAdapter;
use Illuminate\Http\Request;

abstract class PaginatedController extends Controller
{
    protected function paginatedResponse($query, $transformer, Request $request): array
    {
        $perPage = min($request->get('per_page', 15), 100); // Max 100 items per page
        $paginator = $query->paginate($perPage);

        $fractal = Fractal::create()
            ->collection($paginator->getCollection())
            ->transformWith($transformer)
            ->paginateWith(new IlluminatePaginatorAdapter($paginator));

        // Handle includes and excludes
        if ($request->has('include')) {
            $fractal->parseIncludes($request->get('include'));
        }

        if ($request->has('exclude')) {
            $fractal->parseExcludes($request->get('exclude'));
        }

        $result = $fractal->toArray();

        // Add custom pagination metadata
        $result['meta']['pagination']['links'] = [
            'first' => $paginator->url(1),
            'last' => $paginator->url($paginator->lastPage()),
            'prev' => $paginator->previousPageUrl(),
            'next' => $paginator->nextPageUrl(),
        ];

        $result['meta']['pagination']['path'] = $paginator->path();
        $result['meta']['pagination']['per_page'] = $paginator->perPage();
        $result['meta']['pagination']['current_page'] = $paginator->currentPage();
        $result['meta']['pagination']['last_page'] = $paginator->lastPage();
        $result['meta']['pagination']['from'] = $paginator->firstItem();
        $result['meta']['pagination']['to'] = $paginator->lastItem();
        $result['meta']['pagination']['total'] = $paginator->total();

        return $result;
    }

    protected function cursorPaginatedResponse($query, $transformer, Request $request): array
    {
        $perPage = min($request->get('per_page', 15), 100);
        $paginator = $query->cursorPaginate($perPage);

        $fractal = Fractal::create()
            ->collection($paginator->items())
            ->transformWith($transformer);

        if ($request->has('include')) {
            $fractal->parseIncludes($request->get('include'));
        }

        $result = $fractal->toArray();

        // Add cursor pagination metadata
        $result['meta']['pagination'] = [
            'per_page' => $paginator->perPage(),
            'next_cursor' => $paginator->nextCursor()?->encode(),
            'prev_cursor' => $paginator->previousCursor()?->encode(),
            'has_more' => $paginator->hasMorePages(),
            'path' => $paginator->path(),
        ];

        return $result;
    }
}
```

### 4.2. Advanced Filtering

Implement sophisticated filtering capabilities:

```php
// app/Services/ProductFilterService.php
<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ProductFilterService
{
    public function applyFilters(Builder $query, Request $request): Builder
    {
        return $query
            ->when($request->has('search'), function ($q) use ($request) {
                $this->applySearch($q, $request->get('search'));
            })
            ->when($request->has('category'), function ($q) use ($request) {
                $this->applyCategory($q, $request->get('category'));
            })
            ->when($request->has('price_range'), function ($q) use ($request) {
                $this->applyPriceRange($q, $request->get('price_range'));
            })
            ->when($request->has('in_stock'), function ($q) use ($request) {
                $this->applyStockFilter($q, $request->boolean('in_stock'));
            })
            ->when($request->has('tags'), function ($q) use ($request) {
                $this->applyTags($q, $request->get('tags'));
            })
            ->when($request->has('rating'), function ($q) use ($request) {
                $this->applyRating($q, $request->get('rating'));
            })
            ->when($request->has('date_range'), function ($q) use ($request) {
                $this->applyDateRange($q, $request->get('date_range'));
            });
    }

    private function applySearch(Builder $query, string $search): void
    {
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%")
              ->orWhereHas('category', function ($categoryQuery) use ($search) {
                  $categoryQuery->where('name', 'like', "%{$search}%");
              });
        });
    }

    private function applyCategory(Builder $query, $categories): void
    {
        $categoryList = is_array($categories) ? $categories : explode(',', $categories);

        $query->whereHas('category', function ($q) use ($categoryList) {
            $q->whereIn('slug', $categoryList);
        });
    }

    private function applyPriceRange(Builder $query, string $priceRange): void
    {
        [$min, $max] = explode('-', $priceRange);

        if ($min !== '*') {
            $query->where('price', '>=', (float) $min);
        }

        if ($max !== '*') {
            $query->where('price', '<=', (float) $max);
        }
    }

    private function applyStockFilter(Builder $query, bool $inStock): void
    {
        if ($inStock) {
            $query->where('stock_quantity', '>', 0);
        }
    }

    private function applyTags(Builder $query, $tags): void
    {
        $tagList = is_array($tags) ? $tags : explode(',', $tags);

        $query->whereHas('tags', function ($q) use ($tagList) {
            $q->whereIn('name', $tagList);
        });
    }

    private function applyRating(Builder $query, float $minRating): void
    {
        $query->whereHas('reviews', function ($q) use ($minRating) {
            $q->havingRaw('AVG(rating) >= ?', [$minRating]);
        });
    }

    private function applyDateRange(Builder $query, string $dateRange): void
    {
        [$start, $end] = explode(',', $dateRange);

        $query->whereBetween('created_at', [
            \Carbon\Carbon::parse($start)->startOfDay(),
            \Carbon\Carbon::parse($end)->endOfDay(),
        ]);
    }

    public function applySorting(Builder $query, Request $request): Builder
    {
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');

        return match ($sortBy) {
            'price' => $query->orderBy('price', $sortDirection),
            'name' => $query->orderBy('name', $sortDirection),
            'created_at' => $query->orderBy('created_at', $sortDirection),
            'popularity' => $query->withCount('orders')->orderBy('orders_count', $sortDirection),
            'rating' => $query->withAvg('reviews', 'rating')->orderBy('reviews_avg_rating', $sortDirection),
            'stock' => $query->orderBy('stock_quantity', $sortDirection),
            default => $query->orderBy('name', 'asc'),
        };
    }
}
```

### 4.3. Sorting & Ordering

Implement flexible sorting options:

```php
// app/Http/Controllers/Api/SortableProductController.php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Transformers\ProductTransformer;
use App\Services\ProductFilterService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SortableProductController extends PaginatedController
{
    public function __construct(
        private ProductFilterService $filterService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $query = Product::query()->with(['category', 'images']);

        // Apply filters
        $query = $this->filterService->applyFilters($query, $request);

        // Apply sorting
        $query = $this->filterService->applySorting($query, $request);

        $result = $this->paginatedResponse($query, ProductTransformer::class, $request);

        // Add sorting metadata
        $result['meta']['sorting'] = [
            'sort_by' => $request->get('sort_by', 'name'),
            'sort_direction' => $request->get('sort_direction', 'asc'),
            'available_sorts' => [
                'name' => 'Name',
                'price' => 'Price',
                'created_at' => 'Date Added',
                'popularity' => 'Popularity',
                'rating' => 'Customer Rating',
                'stock' => 'Stock Level',
            ],
        ];

        // Add filter metadata
        $result['meta']['filters'] = [
            'applied' => $this->getAppliedFilters($request),
            'available' => $this->getAvailableFilters(),
        ];

        return response()->json($result);
    }

    private function getAppliedFilters(Request $request): array
    {
        $applied = [];

        if ($request->has('search')) {
            $applied['search'] = $request->get('search');
        }

        if ($request->has('category')) {
            $applied['category'] = $request->get('category');
        }

        if ($request->has('price_range')) {
            $applied['price_range'] = $request->get('price_range');
        }

        if ($request->has('in_stock')) {
            $applied['in_stock'] = $request->boolean('in_stock');
        }

        if ($request->has('tags')) {
            $applied['tags'] = $request->get('tags');
        }

        if ($request->has('rating')) {
            $applied['rating'] = $request->get('rating');
        }

        return $applied;
    }

    private function getAvailableFilters(): array
    {
        return [
            'categories' => \App\Models\Category::pluck('name', 'slug')->toArray(),
            'price_ranges' => [
                '0-25' => 'Under $25',
                '25-50' => '$25 - $50',
                '50-100' => '$50 - $100',
                '100-*' => 'Over $100',
            ],
            'tags' => \App\Models\Tag::pluck('name')->toArray(),
            'rating_options' => [
                '4' => '4+ Stars',
                '3' => '3+ Stars',
                '2' => '2+ Stars',
                '1' => '1+ Stars',
            ],
        ];
    }
}
```

---

## Navigation

**← Previous:** [Laravel Data Guide](060-laravel-data-guide.md)

**Next →** [Laravel Sanctum Guide](080-laravel-sanctum-guide.md)
