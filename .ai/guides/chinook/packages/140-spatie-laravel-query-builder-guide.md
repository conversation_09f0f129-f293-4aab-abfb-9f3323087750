# Spatie Laravel Query Builder Guide

## Table of Contents

- [Overview](#overview)
- [Installation & Configuration](#installation--configuration)
- [Basic Usage](#basic-usage)
- [Advanced Features](#advanced-features)
- [Integration with Chinook](#integration-with-chinook)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Navigation](#navigation)

## Overview

The Spatie Laravel Query Builder package provides a powerful and flexible way to build database queries from HTTP request parameters. This guide demonstrates how to integrate and use this package within the Chinook music database system for creating dynamic, filterable API endpoints.

**Key Features:**
- **Request-Based Filtering**: Automatic query building from URL parameters
- **Relationship Filtering**: Filter and include related models
- **Sorting & Pagination**: Built-in sorting and pagination support
- **Security**: Whitelist-based approach for secure query building
- **Laravel 12 Compatibility**: Full support for modern Laravel 12 patterns

## Installation & Configuration

### Package Installation

Install the package using Composer:

```bash
composer require spatie/laravel-query-builder
```

### Basic Configuration

No additional configuration is required, but you can publish the config file for customization:

```bash
php artisan vendor:publish --provider="Spatie\QueryBuilder\QueryBuilderServiceProvider" --tag="config"
```

### Configuration File

Configure the package in `config/query-builder.php`:

```php
<?php

return [
    /*
     * By default the package will use the `include`, `filter`, `sort`
     * and `fields` query parameters as described in the readme.
     */
    'parameters' => [
        'include' => 'include',
        'filter' => 'filter',
        'sort' => 'sort',
        'fields' => 'fields',
        'append' => 'append',
    ],

    /*
     * Related model counts are included using the relationship name suffixed with this string.
     */
    'count_suffix' => 'Count',

    /*
     * By default the package will throw an `InvalidFilterQuery` exception when a filter in the URL
     * is not allowed in the `allowedFilters()` method.
     */
    'disable_invalid_filter_query_exception' => false,

    /*
     * By default the package will throw an `InvalidSortQuery` exception when a sort in the URL
     * is not allowed in the `allowedSorts()` method.
     */
    'disable_invalid_sort_query_exception' => false,

    /*
     * By default the package will throw an `InvalidIncludeQuery` exception when an include in the URL
     * is not allowed in the `allowedIncludes()` method.
     */
    'disable_invalid_include_query_exception' => false,
];
```

## Basic Usage

### Simple Query Building

Create a basic API endpoint for tracks:

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Track;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class TrackController extends Controller
{
    public function index(Request $request)
    {
        $tracks = QueryBuilder::for(Track::class)
            ->allowedFilters(['name', 'unit_price', 'album.title'])
            ->allowedSorts(['name', 'unit_price', 'created_at'])
            ->allowedIncludes(['album', 'mediaType', 'categories'])
            ->paginate($request->get('per_page', 15));

        return response()->json($tracks);
    }
}
```

### Usage Examples

```bash
# Filter tracks by name
GET /api/tracks?filter[name]=rock

# Filter by price range
GET /api/tracks?filter[unit_price]=0.99

# Sort by name ascending
GET /api/tracks?sort=name

# Sort by price descending
GET /api/tracks?sort=-unit_price

# Include related models
GET /api/tracks?include=album,mediaType

# Combine filters, sorting, and includes
GET /api/tracks?filter[name]=rock&sort=-unit_price&include=album
```

## Advanced Features

### Custom Filters

Create custom filter classes for complex filtering logic:

```php
<?php

namespace App\Filters;

use Spatie\QueryBuilder\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class PriceRangeFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        if (is_array($value) && count($value) === 2) {
            return $query->whereBetween('unit_price', [
                (float) $value[0],
                (float) $value[1]
            ]);
        }

        return $query;
    }
}
```

### Genre Filter with Categories

Create a filter for music genres using the category system:

```php
<?php

namespace App\Filters;

use Spatie\QueryBuilder\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class GenreFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        return $query->whereHas('categories', function (Builder $query) use ($value) {
            $query->where('type', 'GENRE')
                  ->where('slug', $value);
        });
    }
}
```

### Advanced Track API Controller

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Track;
use App\Filters\PriceRangeFilter;
use App\Filters\GenreFilter;
use App\Filters\DurationFilter;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;
use Spatie\QueryBuilder\AllowedInclude;

class TrackController extends Controller
{
    public function index(Request $request)
    {
        $tracks = QueryBuilder::for(Track::class)
            ->allowedFilters([
                'name',
                'album.title',
                'album.artist.name',
                AllowedFilter::exact('media_type_id'),
                AllowedFilter::custom('price_range', new PriceRangeFilter()),
                AllowedFilter::custom('genre', new GenreFilter()),
                AllowedFilter::custom('duration', new DurationFilter()),
                AllowedFilter::scope('popular'),
                AllowedFilter::scope('recent'),
            ])
            ->allowedSorts([
                'name',
                'unit_price',
                'milliseconds',
                'created_at',
                AllowedSort::field('album_title', 'album.title'),
                AllowedSort::field('artist_name', 'album.artist.name'),
            ])
            ->allowedIncludes([
                'album',
                'album.artist',
                'mediaType',
                'categories',
                'playlists',
            ])
            ->allowedFields([
                'tracks.id',
                'tracks.name',
                'tracks.unit_price',
                'tracks.milliseconds',
                'album.title',
                'album.artist.name',
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return response()->json($tracks);
    }

    public function show(Request $request, Track $track)
    {
        $track = QueryBuilder::for(Track::where('id', $track->id))
            ->allowedIncludes([
                'album',
                'album.artist',
                'mediaType',
                'categories',
                'playlists',
                'invoiceLines',
            ])
            ->first();

        return response()->json($track);
    }
}
```

### Model Scopes for Query Builder

Add useful scopes to your Track model:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Track extends Model
{
    public function scopePopular(Builder $query): Builder
    {
        return $query->withCount('invoiceLines')
                    ->orderBy('invoice_lines_count', 'desc');
    }

    public function scopeRecent(Builder $query): Builder
    {
        return $query->where('created_at', '>=', now()->subDays(30));
    }

    public function scopeByGenre(Builder $query, string $genre): Builder
    {
        return $query->whereHas('categories', function (Builder $query) use ($genre) {
            $query->where('type', 'GENRE')
                  ->where('slug', $genre);
        });
    }

    public function scopeByPriceRange(Builder $query, float $min, float $max): Builder
    {
        return $query->whereBetween('unit_price', [$min, $max]);
    }
}
```

## Integration with Chinook

### Album API with Advanced Filtering

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Album;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;

class AlbumController extends Controller
{
    public function index(Request $request)
    {
        $albums = QueryBuilder::for(Album::class)
            ->allowedFilters([
                'title',
                'artist.name',
                AllowedFilter::exact('artist_id'),
                AllowedFilter::scope('with_tracks'),
                AllowedFilter::scope('by_year'),
                AllowedFilter::callback('release_year', function (Builder $query, $value) {
                    $query->whereYear('release_date', $value);
                }),
            ])
            ->allowedSorts([
                'title',
                'release_date',
                'created_at',
                AllowedSort::field('artist_name', 'artist.name'),
                AllowedSort::field('track_count', 'tracks_count'),
            ])
            ->allowedIncludes([
                'artist',
                'tracks',
                'tracks.categories',
                'tracks.mediaType',
            ])
            ->withCount('tracks')
            ->defaultSort('-release_date')
            ->paginate($request->get('per_page', 15));

        return response()->json($albums);
    }
}
```

### Artist API with Relationship Filtering

```php
<?php

namespace App\Http\Controllers\Api;

use App\Models\Artist;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class ArtistController extends Controller
{
    public function index(Request $request)
    {
        $artists = QueryBuilder::for(Artist::class)
            ->allowedFilters([
                'name',
                AllowedFilter::scope('with_albums'),
                AllowedFilter::scope('popular'),
                AllowedFilter::callback('has_tracks_in_genre', function (Builder $query, $value) {
                    $query->whereHas('albums.tracks.categories', function (Builder $query) use ($value) {
                        $query->where('type', 'GENRE')
                              ->where('slug', $value);
                    });
                }),
            ])
            ->allowedSorts([
                'name',
                'created_at',
                AllowedSort::field('album_count', 'albums_count'),
                AllowedSort::field('track_count', 'tracks_count'),
            ])
            ->allowedIncludes([
                'albums',
                'albums.tracks',
                'albums.tracks.categories',
            ])
            ->withCount(['albums', 'tracks'])
            ->defaultSort('name')
            ->paginate($request->get('per_page', 15));

        return response()->json($artists);
    }
}
```

### Filament Integration

Use Query Builder in Filament table filters:

```php
<?php

namespace App\Filament\Resources;

use App\Models\Track;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\QueryBuilder;

class TrackResource extends Resource
{
    protected static ?string $model = Track::class;

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable(),
                TextColumn::make('album.title')->searchable(),
                TextColumn::make('album.artist.name')->searchable(),
                TextColumn::make('unit_price')->money('USD'),
                TextColumn::make('milliseconds')->formatStateUsing(fn ($state) => 
                    gmdate('i:s', $state / 1000)
                ),
            ])
            ->filters([
                SelectFilter::make('media_type_id')
                    ->relationship('mediaType', 'name'),
                    
                Filter::make('price_range')
                    ->form([
                        TextInput::make('price_from')->numeric(),
                        TextInput::make('price_to')->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['price_from'], fn (Builder $query, $price) => 
                                $query->where('unit_price', '>=', $price)
                            )
                            ->when($data['price_to'], fn (Builder $query, $price) => 
                                $query->where('unit_price', '<=', $price)
                            );
                    }),
                    
                SelectFilter::make('genre')
                    ->options(function () {
                        return Category::where('type', 'GENRE')
                            ->pluck('name', 'slug');
                    })
                    ->query(function (Builder $query, $value): Builder {
                        return $query->whereHas('categories', function (Builder $query) use ($value) {
                            $query->where('type', 'GENRE')
                                  ->where('slug', $value);
                        });
                    }),
            ]);
    }
}
```

## Testing

### API Testing with Query Builder

Test your API endpoints with Pest:

```php
<?php

use App\Models\Track;
use App\Models\Album;
use App\Models\Artist;

describe('Track API with Query Builder', function () {
    beforeEach(function () {
        $this->artist = Artist::factory()->create(['name' => 'Test Artist']);
        $this->album = Album::factory()->create([
            'title' => 'Test Album',
            'artist_id' => $this->artist->id,
        ]);
        $this->track = Track::factory()->create([
            'name' => 'Test Track',
            'album_id' => $this->album->id,
            'unit_price' => 0.99,
        ]);
    });

    it('can filter tracks by name', function () {
        $response = $this->getJson('/api/tracks?filter[name]=Test');
        
        $response->assertStatus(200)
                ->assertJsonCount(1, 'data')
                ->assertJsonPath('data.0.name', 'Test Track');
    });

    it('can sort tracks by price', function () {
        Track::factory()->create(['unit_price' => 1.99]);
        
        $response = $this->getJson('/api/tracks?sort=-unit_price');
        
        $response->assertStatus(200)
                ->assertJsonPath('data.0.unit_price', '1.99');
    });

    it('can include related models', function () {
        $response = $this->getJson('/api/tracks?include=album,album.artist');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'album' => [
                                'id',
                                'title',
                                'artist' => [
                                    'id',
                                    'name',
                                ],
                            ],
                        ],
                    ],
                ]);
    });

    it('validates allowed filters', function () {
        $response = $this->getJson('/api/tracks?filter[invalid_field]=value');
        
        $response->assertStatus(400);
    });
});
```

## Troubleshooting

### Common Issues

**Invalid Filter/Sort Exceptions**
- Ensure all filters and sorts are explicitly allowed in the controller
- Check that relationship names match your model relationships
- Verify custom filter classes implement the correct interface

**Performance Issues**
- Use `select()` to limit returned fields
- Implement proper database indexes for filtered/sorted columns
- Consider using `allowedFields()` to limit data transfer

**Relationship Loading**
- Use `allowedIncludes()` to control which relationships can be loaded
- Implement proper eager loading to avoid N+1 queries
- Consider using `withCount()` for relationship counts

### Debug Tips

```php
// Enable query logging to debug generated SQL
DB::enableQueryLog();
$tracks = QueryBuilder::for(Track::class)->get();
dd(DB::getQueryLog());

// Use explain to analyze query performance
$query = QueryBuilder::for(Track::class)->toSql();
DB::select("EXPLAIN $query");
```

---

## Navigation

**← Previous:** [Spatie Laravel Settings Guide](130-spatie-laravel-settings-guide.md)

**Next →** [Spatie Laravel Translatable Guide](150-spatie-laravel-translatable-guide.md)

**↑ Back to:** [Package Index](000-packages-index.md)
